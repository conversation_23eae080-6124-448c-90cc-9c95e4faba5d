from django.contrib.auth.models import User, Group
from django.test import TransactionTestCase
from adverse_drug_reaction.models import (
    AdverseDrugReaction,
    AdverseDrugReactionVisitorVersion,
)
from adverse_drug_reaction.tests.factory import AdverseDrugReactionFactory
from base.services.permissions.manage_permissions import PermissionsManagement
from base.tests.factory import *
from rest_framework.test import APIClient
from datetime import datetime, timedelta
import jwt
from core.settings import SECRET_KEY
from base.tests.base_setup import BaseTestSetup
from tasks.tests.factory import (
    ReviewGroupFactory,
    ReviewProcessFactory,
    ReviewProcessTasksFactory,
    ReviewTemplateFactory,
    ReviewTemplateTaskFactory,
)


class TestGetADREndpoints(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.endpoint = "/api/incidents"
        self.department = self.super_user_dept
        self.facility = self.super_user_fac
        """Set up basic test environment"""

    def test_get_incidents_as_super_user(self):
        """Test incident retrieval as super user"""
        for _ in range(3):
            AdverseDrugReactionFactory(
                department=self.department, report_facility=self.facility
            )

        AdverseDrugReactionFactory()

        self._authenticate_user(self.super_user)
        response = self.client.get(f"{self.endpoint}/adverse-drug-reaction/")

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 4)


class GetsGetIncidentDetails(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.endpoint = "/api/incidents"
        self.department = self.super_user_dept
        self.facility = self.super_user_fac
        """Set up basic test environment"""

    def test_get_incident_details_as_super_user(self):
        """Test incident retrieval as super user"""
        incident = AdverseDrugReactionFactory(
            department=self.department, report_facility=self.facility
        )

        self._authenticate_user(self.super_user)
        response = self.client.get(
            f"{self.endpoint}/adverse-drug-reaction/{incident.id}/"
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["incident"]["id"], incident.id)


class GetCreateADREndPointsCase(BaseTestSetup):
    # test create incident as normal user

    def test_create_incident_as_user(self):
        ard_data = {
            "patient_name": {
                "first_name": "Ngoga",
                "last_name": "Henry",
                "profile_type": "Patient",
                # // "user_id": 1
            },
            "observers_name": {
                "first_name": "Ngoga",
                "last_name": "Henry",
                "profile_type": "Observer",
                # // "user_id": 1
            },
            "name_of_physician_notified": {
                "first_name": "Ngoga",
                "last_name": "Henry",
                "profile_type": "Physician",
                # // "user_id": 1
            },
            "name_of_family_notified": {
                "first_name": "Ngoga",
                "last_name": "Henry",
                "profile_type": "Family",
                # // "user_id": 1
            },
            "notified_by": {
                "first_name": "Ngoga",
                "last_name": "Henry",
                "profile_type": "User",
            },
            "date_of_birth": "1980-01-01",
            "date_of_error": "2024-06-26",
            "time_of_error": "14:30:00",
            "location": "Hospital Room 101",
        }

        self._authenticate_user(self.user_user)

        response = self.client.post(
            f"{self.incidents_endpoint}/adverse-drug-reaction/",
            ard_data,
            format="json",
        )
        self.assertEqual(response.status_code, 201)

    def test_create_incident_existing_user(self):
        """Test creating an incident with existing users"""

        patient = UserProfileFactory(profile_type="Patient")
        phycian = UserProfileFactory(profile_type="Physician", patient=patient)
        family_member = UserProfileFactory(profile_type="Family", patient=patient)
        observer = UserProfileFactory(profile_type="Observer", patient=patient)
        notified_by = UserProfileFactory(profile_type="Staff", patient=patient)

        data = {
            "incident_type": "Adverse Drug Reaction",
            "department": self.super_user_dept.id,
            "facility": self.super_user_fac.id,
            "created_by": self.user_user.id,
            "patient_name": {
                "user_id": patient.id,
            },
            "observers_name": {
                "user_id": observer.id,
            },
            "name_of_family_notified": {
                "user_id": family_member.id,
            },
            "name_of_physician_notified": {
                "user_id": phycian.id,
            },
            "notified_by": {
                "user_id": notified_by.id,
            },
            "incident_date": "2023-10-01",
        }

        self._authenticate_user(self.user_user)

        response = self.client.post(
            f"{self.incidents_endpoint}/adverse-drug-reaction/",
            data,
            format="json",
        )
        self.assertEqual(response.status_code, 201)


class TestUpdateADREndpoints(BaseTestSetup):

    def test_update_incident_as_user(self):
        """Test updating an incident as super user"""
        incident = AdverseDrugReactionFactory(created_by=self.user_user)
        self._authenticate_user(self.super_user)
        # Step 1: Create the incident first
        self._authenticate_user(self.user_user)

        update_data = {
            "status": "Open",
            "patient_name": {
                "first_name": "Ngoga",
                "last_name": "Henry",
                "profile_type": "Patient",
            },
            "observers_name": {
                "first_name": "Ngoga",
                "last_name": "Henry",
                "profile_type": "Observer",
            },
            "name_of_physician_notified": {
                "first_name": "Ngoga",
                "last_name": "Henry",
                "profile_type": "Physician",
            },
            "name_of_family_notified": {
                "first_name": "Ngoga",
                "last_name": "Henry",
                "profile_type": "Family",
            },
            "notified_by": {
                "first_name": "FirstNotifier",
                "last_name": "SecNotifier",
                "profile_type": "Staff"
            },
            "date_of_birth": "1980-01-01",
            "date_of_error": "2024-06-26",
            "time_of_error": "15:45",
            "location": "Updated Room 202",
            "suspected_medication": "Paracetamol",
            "dose": "600 mg",
            "route": "Oral",
            "frequency": "Twice daily",
            "rate_of_administration": "30 mg/min",
            "date_of_medication_order": "2024-07-08",
            "other_route_description": "Not applicable",
            "date_of_information": "2024-07-08",
            "nurse_note": True,
            "progress_note": True,
            "other_information_can_be_found_in": False,
            "other_information_description": "Additional details",
            "information_reaction": "Rash",
            "date_of_adverse_reaction": "2024-07-07",
            "reaction_on_settime": "15:45",
            "reaction_was_treated": True,
            "treatment_description": "Antihistamines administered",
            "incident_type_classification": "Type A Reaction",
        }

        update_response = self.client.put(
            f"{self.incidents_endpoint}/adverse-drug-reaction/{incident.id}/",
            update_data,
            format="json",
        )

        self.assertEqual(update_response.status_code, 200)
        self.assertEqual(update_response.data["status"], "Open")
        self.assertEqual(update_response.data["patient_name"]["first_name"], "Ngoga")
        self.assertEqual(update_response.data["patient_name"]["last_name"], "Henry")


class TestMarkAsClosed(BaseTestSetup):
    def test_mark_as_closed_success(self):
        """Test marking a adverse drug reaction as closed successfully"""
        incident = AdverseDrugReactionFactory(created_by=self.user_user)
        self._authenticate_user(self.super_user)

        response = self.client.patch(
            f"{self.adr_endpoint}/{incident.id}/",
            data={
                "action": "mark-closed",
            },
            format="json",
        )

        self.assertEqual(response.status_code, 200)

        # check if the incident is closed successfully
        incident.refresh_from_db()
        self.assertEqual(incident.status, "Closed")


class TestModifyIncident(BaseTestSetup):
    """Test updating an incident as super user"""

    def setUp(self):
        super().setUp()
        self.incident = AdverseDrugReactionFactory(created_by=self.user_user)

        self._authenticate_user(self.user_user)

    def test_modify_incident_success(self):
        modify_data = {
            "action": "modify",
            "status": "Open",
            "patient_name": {
                "first_name": "Ngoga",
                "last_name": "Henry",
                "profile_type": "Patient",
                "medical_record_number": "1234567",
            },
            "observers_name": {
                "first_name": "Ngoga",
                "last_name": "Henry",
                "profile_type": "Observer",
            },
            "name_of_physician_notified": {
                "first_name": "Ngoga",
                "last_name": "Henry",
                "profile_type": "Physician",
            },
            "name_of_family_notified": {
                "first_name": "Ngoga",
                "last_name": "Henry",
                "profile_type": "Family",
            },
            "notified_by": {
                "first_name": "FirstNotifierr",
                "last_name": "SecNotifierr",
                "profile_type": "Staff",
            },

        }

        response = self.client.patch(
            f"{self.adr_endpoint}/{self.incident.id}/",
            modify_data,
            format="json",
        )
        if not response.status_code == 200:
            self.fail(response.data)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["status"], "Open")
        self.assertIsNotNone(
            AdverseDrugReactionVisitorVersion.objects.filter(
                original_report=self.incident, status="Open"
            ).first()
        )

        # assert related fields
        version = AdverseDrugReactionVisitorVersion.objects.filter(
            original_report=self.incident, status="Open"
        ).first()
        # patient name
        self.assertEqual(version.patient_name.first_name, "Ngoga")
        self.assertEqual(version.patient_name.last_name, "Henry")

        # observer's name
        self.assertEqual(version.observers_name.first_name, "Ngoga")
        self.assertEqual(version.observers_name.last_name, "Henry")

        # name of physician notified
        self.assertEqual(version.name_of_physician_notified.first_name, "Ngoga")
        self.assertEqual(version.name_of_physician_notified.last_name, "Henry")

        # name of family notified
        self.assertEqual(version.name_of_family_notified.first_name, "Ngoga")
        self.assertEqual(version.name_of_family_notified.last_name, "Henry")


class TestSendForReviewService(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.template = ReviewTemplateFactory(
            name="Test Template", description="Test Desc"
        )
        self.incident = AdverseDrugReactionFactory()

        # Create profiles and review group
        self.template_profiles = [ProfileFactory() for _ in range(3)]
        self.group = ReviewGroupFactory()
        self.group.members.add(*self.template_profiles)

        # Create a ReviewTemplateTasks with number_of_days_to_complete
        self.task = ReviewTemplateTaskFactory(
            review_template=self.template,
            require_approval_for_all_groups=True,
        )
        self.task.review_groups.add(self.group)

        # Incident reviewers (optional, depending on model)
        self.incident_profiles = [ProfileFactory() for _ in range(3)]

    def test_handle_review_template_success(self):
        self._authenticate_user(self.admin_user)
        data = {
            "action": "send-for-review",
            "review_template": self.template.id,
        }
        response = self.client.patch(
            f"/api/incidents/adverse-drug-reaction/{self.incident.id}/",
            data,
            format="json",
        )
        if not response.status_code == 200:
            self.fail(f"Failed to handle review template: {response.data}")

        self.incident.refresh_from_db()
        self.assertEqual(response.status_code, 200)
        self.assertIsNotNone(self.incident.review_process)
        self.assertEqual(self.incident.review_process.name, self.template.name)
        self.assertEqual(
            self.incident.review_process.description, self.template.description
        )

        # Verify ReviewProcessTasks
        process_tasks = self.incident.review_process.review_process_tasks.all()
        self.assertEqual(process_tasks.count(), 1)
        process_task = process_tasks.first()

        # Check task fields
        self.assertEqual(process_task.name, self.task.name)
        self.assertEqual(process_task.description, self.task.description)
        self.assertEqual(process_task.task_priority, self.task.task_priority)
        self.assertEqual(
            process_task.require_approval_for_all_groups,
            self.task.require_approval_for_all_groups,
        )
        self.assertEqual(process_task.status, "Pending")

        # Check deadline
        expected_deadline = datetime.now().date() + timedelta(
            days=self.task.number_of_days_to_complete
        )
        self.assertEqual(process_task.deadline, expected_deadline)

        # Check review_groups
        self.assertIn(self.group, process_task.review_groups.all())

    def test_send_test_send_for_a_review_reviewers_success(self):
        self._authenticate_user(self.admin_user)
        data = {
            "action": "send-for-review",
            "assignees": [profile.id for profile in self.template_profiles],
        }
        response = self.client.patch(
            f"/api/incidents/adverse-drug-reaction/{self.incident.id}/",
            data,
            format="json",
        )

        if not response.status_code == 200:
            self.fail(f"Failed to send for a review: {response.data}")

        self.assertEqual(response.status_code, 200)


class TestADRTasksService(BaseTestSetup):

    def setUp(self):
        super().setUp()
        self.reviewer = ProfileFactory()
        review_group = ReviewGroupFactory()
        review_group.members.add(self.user_user_profile)
        review_process = ReviewProcessFactory()
        self.incident = AdverseDrugReactionFactory()
        self.tasks = [ReviewProcessTasksFactory() for _ in range(3)]

        self.tasks_with_review_process = [
            ReviewProcessTasksFactory(
                review_process=review_process,
            )
            for _ in range(3)
        ]
        self.tasks_with_reviewers = [ReviewProcessTasksFactory() for _ in range(3)]

        for task in self.tasks_with_review_process:
            task.review_groups.add(review_group)

        for task in self.tasks_with_reviewers:
            task.reviewers.add(self.reviewer)

        self.incident.review_tasks.set(self.tasks)
        self.incident.review_tasks.add(*self.tasks_with_reviewers)
        self.incident.review_tasks.add(*self.tasks_with_review_process)

    def test_get_incident_tasks(self):
        self._authenticate_user(self.super_user)
        response = self.client.get(
            f"{self.incidents_endpoint}/adverse-drug-reaction/{self.incident.id}/tasks/"
        )

        if not response.status_code == 200:
            self.fail(f"Failed to get tasks: {response.data}")

        self.assertEqual(response.status_code, 200)
        self.assertEqual(
            len(response.data),
            len(
                self.tasks + self.tasks_with_review_process + self.tasks_with_reviewers
            ),
        )

    def test_get_incident_tasks_as_user(self):
        self._authenticate_user(self.user_user)
        response = self.client.get(
            f"{self.incidents_endpoint}/adverse-drug-reaction/{self.incident.id}/tasks/"
        )

        if not response.status_code == 200:
            self.fail(f"Failed to get tasks: {response.data}")

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), len(self.tasks_with_review_process))

    def test_get_incident_tasks_as_reviewer(self):
        self._authenticate_user(self.reviewer.user)
        response = self.client.get(
            f"{self.incidents_endpoint}/adverse-drug-reaction/{self.incident.id}/tasks/"
        )

        if not response.status_code == 200:
            self.fail(f"Failed to get tasks: {response.data}")

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), len(self.tasks_with_reviewers))
