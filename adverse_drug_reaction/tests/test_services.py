from django.test import TestCase, TransactionTestCase
from django.contrib.auth.models import Group, User
from accounts.tests.factory import ProfileFactory, UserProfileFactory
from adverse_drug_reaction.models import AdverseDrugReaction
from adverse_drug_reaction.services.operations import ADRService
from adverse_drug_reaction.services.tasks import ADRTasksService
from adverse_drug_reaction.tests.factory import AdverseDrugReactionFactory
from base.models import Department, Facility
from base.tests.base_setup import BaseTestSetup
from base.tests.factory import DepartmentFactory, FacilityFactory
from tasks.tests.factory import (
    ReviewGroupFactory,
    ReviewProcessFactory,
    ReviewProcessTasksFactory,
)


class TestADRServiceCase(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.service = ADRService(user=self.user_user)

    # def test_get_incident_details(self):
    #     incident = AdverseDrugReactionFactory(created_by=self.user_user)
    #     response = self.service.get_incident_by_id(
    #         incident.id,
    #     )

    #     self.assertEqual(response.success, True)

    # def test_get_incidents_as_admin(self):
    #     """Creating incidents for new facility"""
    #     for _ in range(2):
    #         AdverseDrugReactionFactory(
    #             department=self.admin_user_dept,
    #             report_facility=self.admin_user_fac,
    #             created_by=self.user_user,
    #         )

    #     AdverseDrugReactionFactory(
    #         department=DepartmentFactory(),
    #         report_facility=FacilityFactory(),
    #         created_by=UserFactory(),
    #     )
    #     response = self.service.get_incidents_list()

    #     self.assertEqual(response.success, True)
    #     self.assertEqual(len(response.data), 2)

    def test_get_incidents_as_superuser(self):

        # create 5 new incidents for new facility
        for _ in range(5):
            AdverseDrugReactionFactory(
                department=self.super_user_dept,
                report_facility=self.super_user_fac,
                created_by=self.user_user,
            )

        # create 2 incidents for another facility
        for _ in range(2):
            AdverseDrugReactionFactory()

        response = self.service.get_incidents_list()

        self.assertEqual(response.success, True)
        self.assertEqual(len(response.data), 7)

    # def test_get_incidents_as_director(self):

    #     # create 5 new incidents for new facility
    #     for _ in range(5):
    #         AdverseDrugReactionFactory(
    #             department=self.director_user_dept,
    #             report_facility=self.director_user_fac,
    #             created_by=self.user_user,
    #         )

    #     # create 2 incidents for another facility
    #     for _ in range(2):
    #         AdverseDrugReactionFactory()

    #     response = self.service.get_incidents_list()

    #     self.assertEqual(response.success, True)
    #     self.assertEqual(len(response.data), 5)

    # def test_get_incidents_as_manager(self):

    #     # create 3 new incidents for new department
    #     for _ in range(3):
    #         AdverseDrugReactionFactory(
    #             department=self.pharmacy_manager_user_dept,
    #             report_facility=self.manager_user_fac,
    #             created_by=self.user_user,
    #         )

    #     # create 2 incidents for another facility
    #     for _ in range(2):
    #         AdverseDrugReactionFactory()

    #     self.manager_user_profile.access_to_department.add(
    #         self.pharmacy_manager_user_dept
    #     )
    #     response = self.service.get_incidents_list()
    #     self.assertEqual(response.success, True)
    #     self.assertEqual(len(response.data), 3)

    # def get_incidents_as_normal_user(self):
    #     newuser = UserFactory()
    #     # create 2 incidents for the user
    #     for _ in range(2):
    #         AdverseDrugReactionFactory(
    #             department=self.user_user,
    #             report_facility=self.user_user_fac,
    #             created_by=self.user_user,
    #         )
    #     # create another incident for another user
    #     AdverseDrugReactionFactory()

    #     response = self.service.get_incidents_list()
    #     self.assertEqual(response.success, False)
    #     self.assertEqual(len(response.data), 2)


class TestADRServiceGetIncidentById(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.service = ADRService(user=self.super_user)

    def test_get_incident_by_id(self):
        incident = AdverseDrugReactionFactory(created_by=self.super_user)
        response = self.service.get_incident_by_id(incident.id)

        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)

    def test_get_incident_by_id_not_found(self):
        response = self.service.get_incident_by_id(9999)
        self.assertFalse(response.success)


class TestADRServiceCreateIncident(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.service = ADRService(user=self.super_user)

    def test_create_incident_with_existing_users(self):
        patient = UserProfileFactory(profile_type="Patient")
        phycian = UserProfileFactory(profile_type="Physician", patient=patient)
        family_member = UserProfileFactory(profile_type="Family", patient=patient)
        observer = UserProfileFactory(profile_type="Observer", patient=patient)
        notified_by = UserProfileFactory(profile_type="Staff", patient=patient)

        data = {
            "incident_type": "Adverse Drug Reaction",
            "department": DepartmentFactory().id,
            "facility": FacilityFactory().id,
            "created_by": self.super_user,
            "patient_name": {
                "user_id": patient.id,
            },
            "observers_name": {
                "user_id": observer.id,
            },
            "name_of_family_notified": {
                "user_id": family_member.id,
            },
            "name_of_physician_notified": {
                "user_id": phycian.id,
            },
            "notified_by": {
                "user_id": notified_by.id,
            },
            "incident_date": "2023-10-01",
        }
        response = self.service.create_incident(data)
        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)


class TestADRServiceCreateIncidentWithNewUsers(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.service = ADRService(user=self.super_user)

    def test_create_incident_with_new_users(self):
        data = {
            "incident_type": "Adverse Drug Reaction",
            "department": DepartmentFactory().id,
            "facility": FacilityFactory().id,
            "created_by": self.super_user,
            "patient_name": {
                "first_name": "John",
                "last_name": "Doe",
                "email": "<EMAIL>",
                "profile_type": "Patient",
            },
            "observers_name": {
                "first_name": "Jane",
                "last_name": "Smith",
                "email": "<EMAIL>",
                "profile_type": "Observer",
            },
            "name_of_family_notified": {
                "first_name": "Alice",
                "last_name": "Johnson",
                "email": "<EMAIL>",
                "profile_type": "Family",
            },
            "name_of_physician_notified": {
                "first_name": "Dr. Emily",
                "last_name": "Brown",
                "email": "<EMAIL>",
                "profile_type": "Physician",
            },
            "notified_by": {
                "first_name": "Ngoga",
                "last_name": "Henry",
                "profile_type": "Staff",
            },
            "incident_date": "2023-10-01",
        }
        response = self.service.create_incident(data)

        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)

    def test_create_incident_with_missing_fields(self):
        data = {
            "incident_type": "Adverse Drug Reaction",
            "department": DepartmentFactory().id,
            "facility": FacilityFactory().id,
            "created_by": self.super_user,
            "patient_name": {
                "first_name": "John",
                "last_name": "Doe",
                # Missing profile_type
            },
        }
        response = self.service.create_incident(data)

        self.assertFalse(response.success)


class TestADRServiceUpdateIncident(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.service = ADRService(user=self.super_user)

    def test_update_incident(self):
        incident = AdverseDrugReactionFactory(created_by=self.super_user)
        data = {
            "incident_type": "Adverse Drug Reaction",
            "department": DepartmentFactory().id,
            "facility": FacilityFactory().id,
            "created_by": self.super_user,
            "incident_date": "2023-10-01",
        }
        response = self.service.update_incident(incident.id, data)

        self.assertTrue(response.success)
        self.assertIsNotNone(response.data)


class TestADRTasksService(BaseTestSetup):

    def setUp(self):
        super().setUp()
        self.reviewer = ProfileFactory()
        review_group = ReviewGroupFactory()
        review_group.members.add(self.user_user_profile)
        review_process = ReviewProcessFactory()
        self.incident = AdverseDrugReactionFactory()
        self.tasks = [ReviewProcessTasksFactory() for _ in range(3)]

        self.tasks_with_review_process = [
            ReviewProcessTasksFactory(
                review_process=review_process,
            )
            for _ in range(3)
        ]
        self.tasks_with_reviewers = [ReviewProcessTasksFactory() for _ in range(3)]

        for task in self.tasks_with_review_process:
            task.review_groups.add(review_group)

        for task in self.tasks_with_reviewers:
            task.reviewers.add(self.reviewer)

        self.incident.review_tasks.set(self.tasks)
        self.incident.review_tasks.add(*self.tasks_with_reviewers)
        self.incident.review_tasks.add(*self.tasks_with_review_process)

    def test_get_incident_tasks(self):
        response = ADRTasksService(
            incident_id=self.incident.id, user=self.super_user
        ).get_tasks()
        if not response.success:
            self.fail(f"Failed to get tasks: {response.message}")

        self.assertEqual(response.success, True)
        self.assertEqual(
            len(response.data),
            len(
                self.tasks + self.tasks_with_review_process + self.tasks_with_reviewers
            ),
        )

    def test_get_incident_tasks_as_user(self):
        response = ADRTasksService(
            incident_id=self.incident.id, user=self.user_user
        ).get_tasks()
        if not response.success:
            self.fail(f"Failed to get tasks: {response.message}")

        self.assertEqual(response.success, True)
        self.assertEqual(len(response.data), len(self.tasks_with_review_process))

    def test_get_incident_tasks_as_reviewer(self):
        response = ADRTasksService(
            incident_id=self.incident.id, user=self.reviewer.user
        ).get_tasks()
        if not response.success:
            self.fail(f"Failed to get tasks: {response.message}")

        self.assertEqual(response.success, True)
        self.assertEqual(len(response.data), len(self.tasks_with_reviewers))
