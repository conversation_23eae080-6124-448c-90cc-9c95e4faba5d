from django.urls import path

from api.views.incidents.lost_and_found.new_lost_found import (
    lost_found_incidents_api,
    lost_found_incident_details_api,
    lost_found_incident_documents_api,
)


urlpatterns = [
    path(
        "",
        lost_found_incidents_api,
        name="lost_found_incidents_api",
    ),
    path(
        "<int:id>/",
        lost_found_incident_details_api,
        name="lost_found_incident_details_api",
    ),
    path(
        "<int:incident_id>/documents/",
        lost_found_incident_documents_api,
        name="lost_found_incident_documents_api",
    ),
]