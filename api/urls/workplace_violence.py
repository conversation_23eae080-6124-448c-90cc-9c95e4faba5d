from django.urls import path

from api.views.incidents.worplace_violence import (
    workplace_violence_detail,
    workplace_violence_list_create,
    workplace_violence_documents_api,
)


urlpatterns = [
    path("", workplace_violence_list_create, name="workplace_violence_list_create"),
    path(
        "<int:workplace_violence_id>/",
        workplace_violence_detail,
        name="workplace_violence_detail",
    ),
    path(
        "<int:incident_id>/documents/",
        workplace_violence_documents_api,
        name="workplace_violence_documents_api",
    ),
    # version
    #     path(
    #         "<int:incident_id>/versions/<int:version_id>/",
    #         workplace_incident_version,
    #         name="workplace_incident_version",
    #     ),
    #     path(
    #         "<int:incident_id>/versions/original/",
    #         workplace_incident_original_version,
    #         name="workplace_incident_original_version",
    #     ),
    #     # modify incident
    #     path(
    #         "<int:incident_id>/modify/",
    #         modify_workplace_violence,
    #         name="modify_workplace_violence",
    #     ),
    #     # reviews
    #     path(
    #         "<int:workplace_violence_id>/reviews/new/",
    #         new_work_place_violence_review,
    #         name="work_place_violence_detail",
    #     ),
    #     path(
    #         "<int:workplace_violence_id>/reviews/",
    #         get_work_place_violence_reviews,
    #         name="work_place_violence_detail",
    #     ),
    #     # end of reviews
    #     path(
    #         "<int:incident_id>/documents/new/",
    #         new_work_place_violence_document_api,
    #         name="new_work_place_violence_document_api",
    #     ),
    #     path(
    #         "<int:incident_id>/documents/",
    #         new_work_place_violence_document_list_api,
    #         name="new_work_place_violence_document_list_api",
    #     ),
    #     path(
    #         "<int:incident_id>/documents/<int:document_id>/delete/",
    #         delete_work_place_violence_documents,
    #         name="delete_work_place_violence_documents",
    #     ),
    #     path(
    #         "<int:workplace_violence_id>/resolve/",
    #         mark_workplace_violence_as_resolved,
    #         name="mark_workplace_violence_as_resolved",
    #     ),
    #     # send to department
    #     path(
    #         "<int:incident_id>/send-to-department/",
    #         send_workplace_violence_to_department,
    #         name="send_workplace_violence_to_department",
    #     ),
    #     # deleting draft incidents
    #     path(
    #         "drafts/delete-multiple/",
    #         delete_workplace_violence_draft_incidents,
    #         name="delete_workplace_violence_draft_incidents",
    #     ),
]
