from rest_framework import status, serializers
from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.parsers import <PERSON><PERSON>NParser

from base.services.auth import verify_user
from complaints.models import Complaint
from reviews.serializers import ComplaintSerializer
from complaints.services.operations import ComplaintsService
from complaints.services.query import ComplaintsQueryService
from base.services.logging.logger import LoggingService


@api_view(["GET", "POST"])
@permission_classes([IsAuthenticated])
def complaints_list_create_api(request):
    try:
        if request.method == "GET":
            query_service = ComplaintsQueryService()
            # Extract query parameters for filtering
            filters = request.query_params.dict()
            page = int(request.GET.get("page", 1))
            page_size = int(request.GET.get("page_size", 10))

            response = query_service.get_complaints(
                filters=filters if filters else {}, page=page, page_size=page_size
            )
            return Response(response.data, status=response.code)

        elif request.method == "POST":
            service = ComplaintsService()
            complaint_data = request.data
            response = service.create_complaint(complaint_data, request.user)
            return Response(response.data, status=response.code)

    except ValueError as e:
        # Handle pagination errors
        return Response(
            {"error": "Invalid page or page_size parameter"},
            status=status.HTTP_400_BAD_REQUEST,
        )
    except Exception as e:
        LoggingService().log_error(e)
        return Response(
            {"error": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET", "PUT", "DELETE", "PATCH"])
@permission_classes([IsAuthenticated])
def complaints_details_api(request, complaint_id):
    try:
        query_service = ComplaintsQueryService()
        if request.method == "GET":
            response = query_service.get_complaint_by_id(complaint_id)
            return Response(response.data, status=response.code)

        elif request.method == "PUT":
            service = ComplaintsService()
            response = service.update_complaint(complaint_id, request.data)
            return Response(response.data, status=response.code)

        elif request.method == "DELETE":
            service = ComplaintsService()
            response = service.delete_complaint(complaint_id)
            return Response(response.data, status=response.code)

        elif request.method == "PATCH":
            # Partial update - not implemented yet
            return Response(
                {"error": "PATCH method not implemented yet"},
                status=status.HTTP_501_NOT_IMPLEMENTED,
            )

    except Exception as e:
        LoggingService().log_error(e)
        return Response(
            {"error": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
