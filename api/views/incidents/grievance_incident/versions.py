from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated

from patient_visitor_grievance.models import Grievance, GrievanceVersion
from patient_visitor_grievance.serializers import GrievanceListSerializer


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def grievance_version(request, incident_id, version_id):
    try:
        incident = GrievanceVersion.objects.get(id=version_id)
        serializer = GrievanceListSerializer(incident).data
        return Response(serializer, status=status.HTTP_200_OK)
    except GrievanceVersion.DoesNotExist:
        return Response(
            {"error": "Version not found"}, status=status.HTTP_400_BAD_REQUEST
        )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def grievance_original_version(request, incident_id):
    try:
        incident = Grievance.objects.get(id=incident_id)
        serializer = GrievanceListSerializer(incident).data
        return Response(serializer, status=status.HTTP_200_OK)
    except Grievance.DoesNotExist:
        return Response(
            {"error": "Version not found"}, status=status.HTTP_400_BAD_REQUEST
        )
