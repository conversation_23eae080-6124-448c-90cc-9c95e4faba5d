from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from base.services.forms import check_user_facility
from medication_error.models import MedicationError
from medication_error.services.actions import MedicationErrorActionsService
from medication_error.services.documents import MedicationErrorDocService
from medication_error.services.operations import MedicationErrorService
from base.services.incidents.get_incidents import GetIncidentsService
from base.services.logging.logger import LoggingService


logging_service = LoggingService()
incidents_service = GetIncidentsService()


@api_view(["GET", "POST"])
@permission_classes([IsAuthenticated])
def medication_error_incidents_api(request):
    """
    API view for handling Medication Error incidents.
    """
    medication_error_service = MedicationErrorService(request.user)
    try:
        if request.method == "GET":

            filters = request.query_params.dict()
            incidents = medication_error_service.get_incidents_list(filters=filters)

            if not incidents.success:
                return Response(
                    {"error": incidents.message},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            return Response(
                incidents.data,
                status=status.HTTP_200_OK,
            )
        elif request.method == "POST":
            response = medication_error_service.create_incident(request.data)
            if response.success:
                return Response(response.data, status=status.HTTP_201_CREATED)
            else:
                return Response(response.message, status=status.HTTP_400_BAD_REQUEST)
        else:
            return Response(
                {"error": "Method not allowed"},
                status=status.HTTP_405_METHOD_NOT_ALLOWED,
            )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "An error occurred while processing the request."},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )

@api_view(["GET", "PUT", "PATCH"])
@permission_classes([IsAuthenticated])
def medication_error_incident_details_api(request, id):
    """
    API view for handling Medication Error incident details.
    """
    medication_error_service = MedicationErrorService(request.user)
    try:
        medication_error_action = MedicationErrorActionsService(
            user=request.user,
            incident_id=id,
            data=request.data,
        )
        if request.method == "GET":
            incident = medication_error_service.get_incident_by_id(incident_id=id)
            if not incident.success:
                return Response(
                    {"error": incident.message},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            return Response(
                incident.data,
                status=status.HTTP_200_OK,
            )
        elif request.method == "PUT":
            incident = medication_error_service.update_incident(
                id=id,
                data=request.data,
            )
            if not incident.success:
                return Response(
                    {"error": incident.message},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            return Response(
                incident.data,
                status=status.HTTP_200_OK,
            )
        elif request.method == "PATCH":
            action = request.data.get("action")
            if not action:
                return Response(
                    {"error": "Action is required."},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            if action == "send-for-review":
                response = medication_error_action.send_for_review()
                if not response.success:
                    return Response(
                        {"error": response.message},
                        status=status.HTTP_400_BAD_REQUEST,
                    )
                return Response(
                    response.data,
                    status=status.HTTP_200_OK,
                )
            elif action == "mark-closed":
                response = medication_error_action.mark_closed()
                if not response.success:
                    return Response(
                        {"error": response.message},
                        status=status.HTTP_400_BAD_REQUEST,
                    )
                return Response(
                    response.data,
                    status=status.HTTP_200_OK,
                )
            elif action == "modify":
                response = medication_error_action.modify_incident()
                if not response.success:
                    return Response(
                        {"error": response.message},
                        status=response.code,
                    )
                return Response(
                    response.data,
                    status=status.HTTP_200_OK,
                )
            elif action == "delete-draft":
                response = medication_error_action.delete_medication_error_draft_incidents()
                if not response.success:
                    return Response(
                        {"error": response.message},
                        status=status.HTTP_400_BAD_REQUEST,
                    )
                return Response(
                    response.data,
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {"error": "Invalid action."},
                    status=status.HTTP_400_BAD_REQUEST,
                )
    except MedicationError.DoesNotExist:
        return Response(
            {"error": "Incident not found."},
            status=status.HTTP_404_NOT_FOUND,
        )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "An error occurred while processing the request."},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET", "POST"])
@permission_classes([IsAuthenticated])
def medication_error_incident_documents_api(request, incident_id):
    """
    This function handles the document retrieval for a specific Medication Error incident.
    """
    try:
        service = MedicationErrorDocService(incident_id=incident_id, user=request.user)
        if request.method == "GET":
            params = request.query_params.dict()
            response = service.get_documents(params=params)
            if not response.success:
                return Response(
                    {"error": response.message},
                    status=response.code,
                )
            return Response(
                response.data,
                status=status.HTTP_200_OK,
            )
        elif request.method == "POST":
            files = request.FILES.getlist("files")
            if not files:
                return Response(
                    {"error": "No files provided."},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            response = service.create_document(files=files)
            if not response.success:
                return Response(
                    {"error": response.message},
                    status=response.code,
                )
            return Response(
                response.data,
                status=status.HTTP_201_CREATED,
            )
        else:
            return Response(
                {"error": "Method not allowed"},
                status=status.HTTP_405_METHOD_NOT_ALLOWED,
            )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "An error occurred while processing the request."},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )