from django.contrib.auth.models import Group, User, Permission
from django.contrib.contenttypes.models import ContentType
from accounts.models import Profile
from accounts.serializers import GetProfileSerializer, UserSerializer
from base.services.logging.logger import LoggingService
from base.services.permissions.mixins import (
    BasePermissionsMixin,
    IncidentsPermissionsMixin,
)
from base.services.responses import APIResponse, RepositoryResponse
from django.db.models import Model
from typing import List, Dict
from django.apps import apps
from django.db.models import Q

from base.utils.model_mapping import MODEL_MAPPING

logging_service = LoggingService()


class PermissionsManagement:
    def check_group(self, name, permission_level=None) -> RepositoryResponse:
        # Implement logic to create a new group
        group, created = Group.objects.get_or_create(name=name)
        if not created:

            return RepositoryResponse(
                success=False,
                message="Group already exists",
            )

        return RepositoryResponse(
            success=True,
            message="Group created successfully",
            data=group,
        )

    def add_permissions_to_group(
        self, group: Group, permissions: List[Dict[str, str]], model: Model
    ) -> RepositoryResponse:
        perms = []
        content_type = ContentType.objects.get_for_model(model)
        missing_perms = []
        for permission in permissions:
            try:
                perm = Permission.objects.get(
                    codename=permission["code_name"],
                    content_type=content_type,
                )
                perms.append(perm)
            except Permission.DoesNotExist:
                missing_perms.append(permission["code_name"])

        if perms:

            group.permissions.add(*perms)
            if missing_perms:
                return RepositoryResponse(
                    success=True,
                    message=f"Added {len(perms)} permissions; {len(missing_perms)} not found: {missing_perms}",
                    data=group,
                )
            return RepositoryResponse(
                success=True,
                message="Permissions added successfully",
                data=group,
            )
        return RepositoryResponse(
            success=False,
            message=f"No valid permissions found: {missing_perms}",
        )

    def add_permissions_to_user(
        self, user: User, permissions: List[Dict[str, str]], model: Model
    ) -> RepositoryResponse:
        perms = []
        content_type = ContentType.objects.get_for_model(model)
        missing_perms = []
        for permission in permissions:
            try:
                perm = Permission.objects.get(
                    codename=permission["code_name"],
                    content_type=content_type,
                )
                perms.append(perm)
            except Permission.DoesNotExist:
                missing_perms.append(permission["code_name"])
                continue
            user.user_permissions.add(perm)
            user.save()
        if missing_perms:
            return RepositoryResponse(
                success=True,
                message=f"Added {len(perms)} permissions; {len(missing_perms)} not found: {missing_perms}",
                data=user,
            )

        return RepositoryResponse(
            success=True,
            message="Permissions added successfully",
            data=user,
        )

    def remove_permissions_from_group(
        self, group: Group, permissions: List[Dict[str, str]], model: Model
    ) -> RepositoryResponse:
        perms = []
        content_type = ContentType.objects.get_for_model(model)
        missing_perms = []
        for permission in permissions:
            try:
                perm = Permission.objects.get(
                    codename=permission["code_name"],
                    content_type=content_type,
                )
                perms.append(perm)
            except Permission.DoesNotExist:
                missing_perms.append(permission["code_name"])
                continue
            group.permissions.remove(perm)
        if missing_perms:
            return RepositoryResponse(
                success=True,
                message=f"Removed {len(perms)} permissions; {len(missing_perms)} not found: {missing_perms}",
                data=group,
            )

        return RepositoryResponse(
            success=True,
            message="Permissions removed successfully",
            data=group,
        )

    def remove_permissions_from_user(
        self, user: User, permissions: List[Dict[str, str]], model: Model
    ) -> RepositoryResponse:
        perms = []
        content_type = ContentType.objects.get_for_model(model)
        missing_perms = []
        for permission in permissions:
            try:
                perm = Permission.objects.get(
                    codename=permission["code_name"],
                    content_type=content_type,
                )
                perms.append(perm)
            except Permission.DoesNotExist:
                missing_perms.append(permission["code_name"])
                continue
            user.user_permissions.remove(perm)
        if missing_perms:
            return RepositoryResponse(
                success=True,
                message=f"Removed {len(perms)} permissions; {len(missing_perms)} not found: {missing_perms}",
                data=user,
            )

        return RepositoryResponse(
            success=True,
            message="Permissions removed successfully",
            data=user,
        )

    def assign_user_to_group(self, user: User, group: Group) -> RepositoryResponse:
        try:
            user.groups.add(group)
            user.save()
            return RepositoryResponse(
                success=True,
                message="User assigned to group successfully",
                data=user,
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message="Internal server error",
            )

    def remove_user_from_group(self, user: User, group: Group) -> RepositoryResponse:
        try:
            user.groups.remove(group)
            return RepositoryResponse(
                success=True,
                message="User removed from group successfully",
                data=user,
            )
        except Exception as e:
            return RepositoryResponse(
                success=False,
                message="Internal server error",
            )

    def create_permission(self, user: User, data, model: Model) -> APIResponse:
        # Implement logic to create a new permission
        try:
            if not "group.name" in data or not data.get("group.name"):
                return RepositoryResponse(
                    success=False,
                    message="Group name is required",
                    code=400,
                    data=None,
                )
            group = self.check_group(data.pop("group.name"))
            if not group.success:
                return APIResponse(
                    success=False,
                    message=group.message,
                    code=400,
                    data=None,
                )

            permissions = self.add_permissions_to_group(
                group.data, data.pop("permissions"), model=model
            )
            if not permissions.success:
                return APIResponse(
                    success=False,
                    message=permissions.message,
                    code=400,
                    data=None,
                )

            return APIResponse(
                success=True,
                message="Permission created successfully",
                code=201,
                data=permissions.data,
            )

        except Exception as e:
            return APIResponse(
                success=False,
                message="Internal server error",
                code=500,
                data=None,
            )

    def get_incident_model_permissions(self, model: Model, type: str):
        try:
            content_type = ContentType.objects.get_for_model(model)

            permissions = Permission.objects.filter(
                content_type=content_type,
            )
            permission_list = [
                {"code_name": p.codename, "name": p.name} for p in permissions
            ]
            return APIResponse(
                success=True,
                message="Permissions retrieved successfully",
                code=200,
                data=permission_list,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Internal server error",
                data=None,
            )

    def create_group_with_permissions(self, user, data, model):
        try:
            response = self.create_permission(user, model, data)

            if not response.success:
                return APIResponse(
                    success=False,
                    message=response.message,
                    data=None,
                )

            return APIResponse(
                success=True,
                message="Group created with permissions successfully",
                data=response.data,
            )
        except Exception as e:

            return APIResponse(
                success=False,
                message="Internal server error",
                data=None,
            )

    def get_permissions_groups(
        self,
        user,
        user_id=None,
        params=None,
    ):
        """
        Get all permissions that a group has on every model
        returns a list of permissions:
            [
                {
                    "id": 1,
                    "name": "Super User",
                    "permissions": [
                        {
                            "feature": "General patient visitors",
                            "perms": [
                                {
                                    "code_name": "005",
                                    "name": "Can create"
                                },
                                {
                                    "code_name": "006",
                                    "name": "Can delete"
                                }
                            ]
                        }
                    ]
                }
            ]
        """
        all_models = apps.get_models()
        MODEL_TO_FEATURE = {}
        for feature, model in MODEL_MAPPING.items():
            MODEL_TO_FEATURE[model] = feature

        try:
            if user_id:
                permissions_user = User.objects.get(id=user_id)
                groups = permissions_user.groups.all()
            else:
                groups = Group.objects.prefetch_related("permissions")

            if params and params.get("search"):
                search = params.get("search")
                groups = groups.filter(
                    Q(name__icontains=search) | Q(permissions__name__icontains=search)
                ).distinct()

            if params and params.get("page") and params.get("page_size"):
                page = int(params["page"])
                page_size = int(params["page_size"])
                start = (page - 1) * page_size
                end = start + page_size
                groups = groups[start:end]

            result = []
            for group in groups:
                permissions_list = []

                group_permissions = group.permissions.all()

                for model in all_models:
                    if model in MODEL_TO_FEATURE:
                        model_name = MODEL_TO_FEATURE[model]
                    
                    model_permissions = []

                    for perm in group_permissions:
                        if perm.content_type.model_class() == model:
                            model_permissions.append(
                                {"code_name": perm.codename, "name": perm.name}
                            )

                    if model_permissions:
                        permissions_list.append(
                            {
                                "feature": model_name,
                                "perms": model_permissions,
                            }
                        )

                result.append(
                    {
                        "id": group.id,
                        "name": group.name,
                        "permissions": permissions_list,
                    }
                )

            return APIResponse(
                success=True,
                message="Permissions retrieved successfully",
                data=result,
            )
        except User.DoesNotExist:
            return APIResponse(
                success=False,
                message="User not found",
                data=None,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Internal server error",
                data=None,
                code=500,
            )

    def get_permissions_group_details(self, group_id):
        all_models = apps.get_models()
        MODEL_TO_FEATURE = {}
        for feature, model in MODEL_MAPPING.items():
            MODEL_TO_FEATURE[model] = feature
        
        try:
            permissions_list = []
            group = Group.objects.get(id=group_id)

            group_permissions = group.permissions.all()

            for model in all_models:
                if model in MODEL_TO_FEATURE:
                    model_name = MODEL_TO_FEATURE[model]
                
                model_permissions = []

                for perm in group_permissions:
                    if perm.content_type.model_class() == model:
                        model_permissions.append(
                            {"code_name": perm.codename, "name": perm.name}
                        )

                if model_permissions:
                    permissions_list.append(
                        {"feature": model_name, "perms": model_permissions}
                    )

            if permissions_list:
                {
                    "id": group.id,
                    "name": group.name,
                    "permissions": permissions_list,
                }

            return APIResponse(
                success=True,
                message="Permissions retrieved successfully",
                data={
                    "id": group.id,
                    "name": group.name,
                    "permissions": permissions_list,
                },
            )

        except Group.DoesNotExist:
            return APIResponse(
                success=False,
                message="Group not found",
                data=None,
            )

        except Exception as e:
            return APIResponse(
                success=False,
                message="Internal server error",
                data=None,
            )

    def get_group_users(self, group_id):
        try:
            group = Group.objects.get(id=group_id)
            users = group.user_set.all()

            profiles = []

            for user in users:
                profile = (
                    Profile.objects.filter(user=user)
                    .prefetch_related("access_to_department", "access_to_facilities")
                    .select_related("user", "facility")
                    .first()
                )
                """ Only add profile to the list if it exists to avoid internal server error """
                if profile:
                    profiles.append(profile)

            serializer = GetProfileSerializer(profiles, many=True)
            return APIResponse(
                success=True,
                message="Users retrieved successfully",
                data=serializer.data,
            )

        except Group.DoesNotExist:
            return APIResponse(
                success=False,
                message="Group not found",
                data=None,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Internal server error",
                data=None,
                code=500,
            )

    def check_permissions(
        self, user: User, model: Model, permissions: List[str]
    ) -> RepositoryResponse:
        app_name = model._meta.app_label
        perm_strings = [f"{app_name}.{perm}" for perm in permissions]

        user_perms = {perm: user.has_perm(perm) for perm in perm_strings}
        user_has_all_perms = all(user_perms.values())

        if user_has_all_perms:
            return RepositoryResponse(
                message=f"You have direct permission to {', '.join(permissions)}.",
                success=True,
            )

        group_success = False
        messages = []
        for group in user.groups.all():
            group_perm_codenames = set(
                group.permissions.values_list("codename", flat=True)
            )
            group_perms = {
                perm: perm.split(".")[-1] in group_perm_codenames
                for perm in perm_strings
            }

            if all(group_perms.values()):
                return RepositoryResponse(
                    message=f"You’re in {group.name}, and {group.name} can {', '.join(permissions)}!",
                    success=True,
                )
            else:
                missing = [
                    perm.split(".")[-1] for perm, has in group_perms.items() if not has
                ]
                messages.append(
                    f"You’re in {group.name}, but it lacks '{', '.join(missing)}'."
                )
                group_success = False

        if not user_has_all_perms and not any(
            all(
                group.permissions.filter(codename=perm.split(".")[-1]).exists()
                for perm in perm_strings
            )
            for group in user.groups.all()
        ):
            missing_user_perms = [
                perm.split(".")[-1] for perm, has in user_perms.items() if not has
            ]
            messages.append(
                f"You lack direct permission for '{', '.join(missing_user_perms)}'."
            )
            return RepositoryResponse(
                message="Permissions check failed",
                data=messages,
                success=False,
            )

        return RepositoryResponse(
            message="Permissions check passed",
            data=None,
            success=True,
        )

    def get_user_permissions(self, user_id):
        all_models = apps.get_models()
        MODEL_TO_FEATURE = {}
        for feature, model in MODEL_MAPPING.items():
            MODEL_TO_FEATURE[model] = feature
        
        try:
            permissions_list = []
            user = User.objects.get(id=user_id)

            # Get all permissions: direct user permissions and group permissions
            user_permissions = user.user_permissions.all()
            group_permissions = set()
            for group in user.groups.all():
                group_permissions.update(group.permissions.all())

            # Combine all permissions, removing duplicates
            all_permissions = set(user_permissions) | group_permissions

            for model in all_models:
                if model in MODEL_TO_FEATURE:
                    model_name = MODEL_TO_FEATURE[model]
                
                model_permissions = []

                # Filter permissions related to this model
                for perm in all_permissions:
                    if perm.content_type.model_class() == model:
                        model_permissions.append(
                            {"code_name": perm.codename, "name": perm.name}
                        )

                # Only add the model if it has permissions
                if model_permissions:
                    permissions_list.append(
                        {"feature": model_name, "permissions": model_permissions}
                    )

            return APIResponse(
                success=True,
                message="User permissions retrieved successfully",
                data=permissions_list,
            )

        except User.DoesNotExist:
            return APIResponse(
                success=False,
                message="User not found",
                data=None,
            )

        except Exception as e:
            return APIResponse(
                success=False,
                message="Internal server error",
                data=None,
            )
