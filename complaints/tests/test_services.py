from django.test import TransactionTestCase
from complaints.models import Complaint
from complaints.services.operations import ComplaintsService
from complaints.services.query import ComplaintsQueryService
from complaints.tests.factory import ComplaintFactory
from base.tests.factory import UserFactory, FacilityFactory, DepartmentFactory


class TestCreateComplaint(TransactionTestCase):
    def setUp(self):
        self.service = ComplaintsService()
        self.query_service = ComplaintsQueryService()
        self.facility = FacilityFactory()
        self.department = DepartmentFactory()
        self.user = UserFactory()

        self.data = {
            "complain_facility": self.facility.id,
            "date_of_complaint": "2024-01-15",
            "patient_name": "<PERSON>",
            "phone_number": "************",
            "medical_record_number": "MRN123456",
            "complaint_nature": "Poor service quality",
            "complaint_type": "Service",
            "resolved_by_staff": False,
            "how_complaint_was_taken": "Phone",
            "details": "Patient complained about waiting time",
        }

    def test_create_complaint_success(self):
        response = self.service.create_complaint(self.data, logged_in_user=self.user)

        if not response.success:
            self.fail(f"Failed to create complaint: {response}")
        self.assertTrue(response.success)

        # check if the complaint was created
        complain = Complaint.objects.get(id=response.data["id"])
        self.assertIsNotNone(complain)
        self.assertEqual(complain.patient_name, self.data["patient_name"])
        self.assertEqual(complain.complain_facility, self.facility)

        # check if the complaint is associated with the user
        self.assertEqual(complain.created_by, self.user)

    def test_create_complaint_invalid_data(self):
        invalid_data = {
            "complain_facility": "invalid_facility_id",
            "date_of_complaint": "invalid_date",
        }

        response = self.service.create_complaint(invalid_data, logged_in_user=self.user)

        self.assertFalse(response.success)
        self.assertEqual(response.message, "Invalid complaint data")


class TestUpdateComplaint(TransactionTestCase):
    def setUp(self):
        self.service = ComplaintsService()
        self.query_service = ComplaintsQueryService()
        self.complaint = ComplaintFactory()

        self.update_data = {
            "patient_name": "Jane Doe",
            "phone_number": "************",
            "details": "Updated complaint details",
        }

    def test_update_complaint_success(self):
        response = self.service.update_complaint(
            self.complaint.id, update_data=self.update_data
        )

        if not response.success:
            self.fail(f"Failed to update complaint: {response}")
        self.assertTrue(response.success)

        # check if the complaint was updated
        updated_complaint = Complaint.objects.get(id=self.complaint.id)
        self.assertEqual(
            updated_complaint.patient_name, self.update_data["patient_name"]
        )
        self.assertEqual(
            updated_complaint.phone_number, self.update_data["phone_number"]
        )
        self.assertEqual(updated_complaint.details, self.update_data["details"])

    def test_update_complaint_not_found(self):
        response = self.service.update_complaint(
            complaint_id=9999, update_data=self.update_data
        )

        self.assertFalse(response.success)


class TestDeleteComplaint(TransactionTestCase):
    def setUp(self):
        self.service = ComplaintsService()
        self.query_service = ComplaintsQueryService()
        self.complaint = ComplaintFactory()

    def test_delete_complaint_success(self):
        response = self.service.delete_complaint(self.complaint.id)

        if not response.success:
            self.fail(f"Failed to delete complaint: {response}")
        self.assertTrue(response.success)

        # check if the complaint was deleted
        with self.assertRaises(Complaint.DoesNotExist):
            Complaint.objects.get(id=self.complaint.id)

    def test_delete_complaint_not_found(self):
        response = self.service.delete_complaint(9999)

        self.assertFalse(response.success)


class TestGetComplaintById(TransactionTestCase):
    def setUp(self):
        self.query_service = ComplaintsQueryService()
        self.complaint = ComplaintFactory()

    def test_get_complaint_by_id_success(self):
        response = self.query_service.get_complaint_by_id(self.complaint.id)

        if not response.success:
            self.fail(f"Failed to get complaint: {response}")
        self.assertTrue(response.success)
        self.assertEqual(response.data["id"], self.complaint.id)

    def test_get_complaint_by_id_not_found(self):
        response = self.query_service.get_complaint_by_id(9999)

        self.assertFalse(response.success)


class TestGetAllComplaints(TransactionTestCase):
    def setUp(self):
        self.query_service = ComplaintsQueryService()
        self.complaints = [ComplaintFactory() for _ in range(5)]

    def test_get_all_complaints_success(self):
        response = self.query_service.get_complaints()

        if not response.success:
            self.fail(f"Failed to get all complaints: {response}")
        self.assertTrue(response.success)
        self.assertEqual(len(response.data["results"]), len(self.complaints))

    def test_get_all_complaints_empty(self):
        # Clear existing complaints
        Complaint.objects.all().delete()

        response = self.query_service.get_complaints()

        self.assertTrue(response.success)
        self.assertEqual(len(response.data["results"]), 0)


class TestGetComplaintsWithFIlters(TransactionTestCase):
    def setUp(self):
        self.query_service = ComplaintsQueryService()
        self.facility1 = FacilityFactory()
        self.facility2 = FacilityFactory()
        self.department1 = DepartmentFactory()
        self.department2 = DepartmentFactory()
        self.user1 = UserFactory()
        self.user2 = UserFactory()

        # Create test complaints with specific attributes for filtering
        self.complaint1 = ComplaintFactory(
            complain_facility=self.facility1,
            date_of_complaint="2024-01-15",
            resolved_by_staff=True,
            created_by=self.user1,
        )
        self.complaint1.department.add(self.department1)
        self.complaint1.assigned_to.add(self.user1)

        self.complaint2 = ComplaintFactory(
            complain_facility=self.facility2,
            date_of_complaint="2024-01-20",
            resolved_by_staff=False,
            created_by=self.user2,
        )
        self.complaint2.department.add(self.department2)
        self.complaint2.assigned_to.add(self.user2)

        self.complaint3 = ComplaintFactory(
            complain_facility=self.facility1,
            date_of_complaint="2024-01-25",
            resolved_by_staff=True,
            created_by=self.user1,
        )
        self.complaint3.department.add(self.department1)
        self.complaint3.assigned_to.add(self.user1)

    def test_filter_by_created_by(self):
        """Test filtering complaints by created_by user"""
        filters = {"created_by": self.user1.id}
        response = self.query_service.get_complaints(filters=filters)

        self.assertTrue(response.success)
        results = response.data["results"]
        self.assertEqual(len(results), 2)  # complaint1 and complaint3

        # Verify all results are created by user1
        for result in results:
            self.assertEqual(result["created_by"], self.user1.id)

    def test_filter_by_date_of_complaint(self):
        """Test filtering complaints by date_of_complaint"""
        filters = {"date_of_complaint": "2024-01-15"}
        response = self.query_service.get_complaints(filters=filters)

        self.assertTrue(response.success)
        results = response.data["results"]
        self.assertEqual(len(results), 1)  # only complaint1
        self.assertEqual(results[0]["date_of_complaint"], "2024-01-15")

    def test_filter_by_department(self):
        """Test filtering complaints by department"""
        filters = {"department": self.department1.id}
        response = self.query_service.get_complaints(filters=filters)

        self.assertTrue(response.success)
        results = response.data["results"]
        self.assertEqual(len(results), 2)  # complaint1 and complaint3

        # Verify all results have department1
        for result in results:
            self.assertIn(self.department1.id, result["department"])

    def test_filter_by_resolved_by_staff_true(self):
        """Test filtering complaints by resolved_by_staff=True"""
        filters = {"resolved_by_staff": True}
        response = self.query_service.get_complaints(filters=filters)

        self.assertTrue(response.success)
        results = response.data["results"]
        self.assertEqual(len(results), 2)  # complaint1 and complaint3

        # Verify all results are resolved
        for result in results:
            self.assertTrue(result["resolved_by_staff"])

    def test_filter_by_resolved_by_staff_false(self):
        """Test filtering complaints by resolved_by_staff=False"""
        filters = {"resolved_by_staff": False}
        response = self.query_service.get_complaints(filters=filters)

        self.assertTrue(response.success)
        results = response.data["results"]
        self.assertEqual(len(results), 1)  # only complaint2
        self.assertFalse(results[0]["resolved_by_staff"])

    def test_filter_by_assigned_to(self):
        """Test filtering complaints by assigned_to user"""
        filters = {"assigned_to": self.user1.id}
        response = self.query_service.get_complaints(filters=filters)

        self.assertTrue(response.success)
        results = response.data["results"]
        self.assertEqual(len(results), 2)  # complaint1 and complaint3

        # Verify all results are assigned to user1
        for result in results:
            self.assertIn(self.user1.id, result["assigned_to"])

    def test_multiple_filters(self):
        """Test filtering complaints with multiple filters"""
        filters = {
            "created_by": self.user1.id,
            "resolved_by_staff": True,
            "department": self.department1.id,
        }
        response = self.query_service.get_complaints(filters=filters)

        self.assertTrue(response.success)
        results = response.data["results"]
        self.assertEqual(len(results), 2)  # complaint1 and complaint3

        # Verify all filters are applied
        for result in results:
            self.assertEqual(result["created_by"], self.user1.id)
            self.assertTrue(result["resolved_by_staff"])
            self.assertIn(self.department1.id, result["department"])

    def test_filters_with_no_results(self):
        """Test filtering with filters that return no results"""
        filters = {
            "created_by": self.user1.id,
            "date_of_complaint": "2024-01-20",  # This date belongs to user2's complaint
        }
        response = self.query_service.get_complaints(filters=filters)

        self.assertTrue(response.success)
        results = response.data["results"]
        self.assertEqual(len(results), 0)

    def test_filter_by_created_at_date(self):
        """Test filtering complaints by created_at date"""
        from datetime import date

        today = date.today().strftime("%Y-%m-%d")

        filters = {"created_at": today}
        response = self.query_service.get_complaints(filters=filters)

        self.assertTrue(response.success)
        # Should return all complaints created today (all test complaints)
        results = response.data["results"]
        self.assertEqual(len(results), 3)

    def test_pagination_with_filters(self):
        """Test pagination works correctly with filters"""
        filters = {"created_by": self.user1.id}
        response = self.query_service.get_complaints(
            filters=filters, page=1, page_size=1
        )

        self.assertTrue(response.success)
        data = response.data
        self.assertEqual(len(data["results"]), 1)
        self.assertTrue(data["has_next"])
        self.assertFalse(data["has_previous"])
        self.assertEqual(data["total_count"], 2)
        self.assertEqual(data["total_pages"], 2)

    def test_invalid_filter_values(self):
        """Test handling of invalid filter values"""
        filters = {"created_by": 99999}  # Non-existent user ID
        response = self.query_service.get_complaints(filters=filters)

        self.assertTrue(response.success)
        results = response.data["results"]
        self.assertEqual(len(results), 0)

    def test_empty_filters(self):
        """Test that empty filters return all complaints"""
        filters = {}
        response = self.query_service.get_complaints(filters=filters)

        self.assertTrue(response.success)
        results = response.data["results"]
        self.assertEqual(len(results), 3)  # All complaints
