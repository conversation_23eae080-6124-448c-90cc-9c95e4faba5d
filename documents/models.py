from django.db import models

from base.models import BaseModel
from documents.storage_backends import PrivateMediaStorage


# Create your models here.
class Document(BaseModel):
    document_url = models.URLField()
    public_id = models.CharField(max_length=1000, null=True, blank=True)
    name = models.CharField(max_length=1000, null=True, blank=True)
    file_type = models.CharField(max_length=100, null=True, blank=True)

    def __str__(self):
        return f"{self.name}.{self.file_type}"

    def delete(self, *args, **kwargs):
        # Since we're using external storage (Cloudinary), we don't need to delete the file here
        # The file deletion should be handled by the service layer if needed
        super().delete(*args, **kwargs)
