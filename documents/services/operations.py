from base.services.responses import APIResponse
from documents.models import Document
import os
import cloudinary
import cloudinary.uploader
import boto3
import uuid
from core.settings import (
    CLOUDINARY_NAME,
    CLOUDINARY_API_KEY,
    CLOUDINARY_SECRET_KEY,
)
from django.core.files.base import ContentFile

from documents.views import upload_file

cloudinary.config(
    cloud_name=CLOUDINARY_NAME,
    api_key=CLOUDINARY_API_KEY,
    api_secret=CLOUDINARY_SECRET_KEY,
    secure=True,
)

from base.services.logging.logger import LoggingService

logging_service = LoggingService()

s3_client = boto3.client(
    "s3",
    aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID"),
    aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY"),
    endpoint_url=os.getenv("AWS_S3_ENDPOINT_URL"),
)


class DocumentsOperations:

    def get_document(self, document_id) -> APIResponse:
        """Retrieve a document by its ID."""

        try:
            document = Document.objects.get(id=document_id)
            return APIResponse(
                success=True,
                message="Document retrieved successfully",
                data={
                    "id": document.id,
                    "name": document.name,
                    "file_type": document.file_type,
                    "url": document.document_url if document.document_url else None,
                },
                code=200,
            )
        except Document.DoesNotExist:

            return APIResponse(
                success=False,
                message="Document not found",
                data=None,
                code=404,
            )

    def create_document(self, files, incident, user) -> APIResponse:
        """Create a new document with the provided data."""

        try:
            incident_documents = []

            if not files or len(files) < 1:
                return ("No files provided", [])

            for file in files:
                unique_filename = f"{uuid.uuid4().hex}_{file.name}"
                # Reset file pointer to beginning before upload
                file.seek(0)
                base_name, extension = os.path.splitext(unique_filename)
                
                if len(base_name) > 200:
                    base_name = base_name[:200]

                is_uploaded, document_url, public_id, message = upload_file(
                    file, "folder_name"
                )
                if not is_uploaded:
                    logging_service.log_error(message)
                    raise Exception("File upload failed")
                document = Document.objects.create(
                    name=base_name,
                    file_type=extension,
                    created_by=user,
                    document_url=document_url,
                    public_id=public_id,
                )

                incident.documents.add(document.id)
                incident.save()

            for doc in incident.documents.all():
                incident_documents.append(
                    {
                        "id": doc.id,
                        "name": doc.name,
                        "file_type": doc.file_type,
                        "created_at": doc.created_at,
                        "updated_at": doc.updated_at,
                        "url": doc.document_url,
                    }
                )
            return APIResponse(
                success=True,
                message="Documents created successfully",
                data={"files": incident_documents},
                code=201,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Failed to create document",
                data=None,
                code=500,
            )

    def delete_document(self, document_id) -> APIResponse:
        """Delete a document by its ID."""
        try:
            document = Document.objects.get(id=document_id)
            results = cloudinary.uploader.destroy(document_id)
            if results.get("result") != "ok":
                logging_service.log_error(results)
                return APIResponse(
                    success=False,
                    message="Failed to delete document from Cloudinary",
                    data=None,
                    code=500,
                )
            document.delete()
            return APIResponse(
                success=True,
                message="Document deleted successfully",
                data=None,
                code=200,
            )
        except Document.DoesNotExist:
            return APIResponse(
                success=False,
                message="Document not found",
                data=None,
                code=404,
            )
        except Exception as e:

            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Failed to delete document",
                data=None,
                code=500,
            )
