from django.contrib.auth.models import User
from rest_framework.test import APIClient
from datetime import datetime, timedelta
import jwt
from base.tests.base_setup import BaseTestSetup
from base.tests.factory import *
from core.settings import SECRET_KEY
from django.test import TestCase

from tasks.tests.factory import (
    ReviewGroupFactory,
    ReviewTemplateFactory,
    ReviewTemplateTaskFactory,
)


class TestCreateIncident(BaseTestSetup):
    def setUp(self):
        """Set up test data once for all test methods"""
        super().setUp()
        self.user = UserFactory()
        self.facility = FacilityFactory()
        self.profile = ProfileFactory(user=self.user, facility=self.facility)
        self.endpoint = "/api/incidents/general-visitor/"

        self.base_incident_data = {
            "category": "Outpatient",
            "facility_id": self.facility.id,
            "report_facility_id": self.facility.id,
            "incident_date": "2024-01-01",
            "incident_time": "08:00",
            "patient_visitor": {
                "first_name": "<PERSON>",
                "last_name": "<PERSON><PERSON>",
                "phone_number": "**********",
                "gender": "male",
                "date_of_birth": "1990-01-01",
                "address": "123 Main St",
                "birth_country": "USA",
                "city": "San Francisco",
                "state": "CA",
                "zip_code": "94107",
                "medical_record_number": self.patient.medical_record_number,
                "profile_type": "Patient",
            },
        }

        # Authenticate once for all tests
        self._authenticate_user(self.user)

    def _authenticate_user(self, user):
        """Helper method to authenticate a user"""
        token_payload = {
            "user_id": user.id,
            "exp": datetime.now() + timedelta(days=1),
        }
        jwt_token = jwt.encode(token_payload, SECRET_KEY, algorithm="HS256")

        self.client = APIClient()
        self.client.force_authenticate(user=user)
        self.client.credentials(HTTP_AUTHORIZATION=f"Bearer {jwt_token}")

    def _create_incident(self, additional_data=None):
        """Helper method to create an incident with optional additional data"""
        incident_data = self.base_incident_data.copy()
        if additional_data:
            incident_data.update(additional_data)
        return self.client.post(self.endpoint, incident_data, format="json")

    def test_create_basic_incident(self):
        """Test creating a basic incident with minimal required data"""
        response = self._create_incident()
        self.assertEqual(response.status_code, 201)
        self.assertIn("id", response.data)  # Verify we get an ID back

    def test_create_incident_with_family_notification(self):
        """Test creating an incident with family notification data"""
        family_data = {
            "family_notified": {
                "first_name": "Jane",
                "last_name": "Smith",
                "email": "<EMAIL>",
                "relationship_to_patient": "Mother",
                "profile_type": "Family",
            }
        }
        response = self._create_incident(family_data)
        self.assertEqual(response.status_code, 201)
        self.assertIn("family_notified", response.data)

    def test_create_incident_with_physician_notification(self):
        """Test creating an incident with physician notification data"""
        physician_data = {
            "physician_notified": {
                "first_name": "Dr",
                "last_name": "Smith",
                "phone_number": "**********",
                "gender": "female",
                "date_of_birth": "1980-01-01",
                "profile_type": "Physician",
            }
        }
        response = self._create_incident(physician_data)
        self.assertEqual(response.status_code, 201)
        self.assertIn("physician_notified", response.data)

    def test_create_complete_incident(self):
        """Test creating an incident with all possible notification data"""
        complete_data = {
            "family_notified": {
                "first_name": "Jane",
                "last_name": "Smith",
                "email": "<EMAIL>",
                "relationship_to_patient": "Mother",
                "profile_type": "Family",
            },
            "physician_notified": {
                "first_name": "Dr",
                "last_name": "Smith",
                "phone_number": "**********",
                "gender": "female",
                "date_of_birth": "1980-01-01",
                "profile_type": "Physician",
            },
            "notified_by": {
                "first_name": "Nurse",
                "last_name": "Johnson",
                "phone_number": "**********",
                "gender": "female",
                "profile_type": "Nurse",
            },
        }
        response = self._create_incident(complete_data)
        self.assertEqual(response.status_code, 201)
        self.assertIn("family_notified", response.data)
        self.assertIn("physician_notified", response.data)
        self.assertIn("notified_by", response.data)


class TestGetIncidents(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.endpoint = "/api/incidents"
        self.department = self.super_user_dept
        self.facility = self.super_user_fac

    def test_get_incidents_as_super_user(self):
        """Test incident retrieval as super user"""
        for _ in range(3):
            GeneralPatientVisitorFactory(
                department=self.department, report_facility=self.facility
            )

        GeneralPatientVisitorFactory()

        self._authenticate_user(self.super_user)
        response = self.client.get(f"{self.endpoint}/general-visitor/")

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 4)
    
    def test_get_incident_with_filters(self):
        """Test incident retrieval with filters"""
        for _ in range(3):
            GeneralPatientVisitorFactory(
                department=self.department,
                report_facility=self.facility,
                created_by=self.super_user,
                category="Outpatient",
                incident_type="General",
            )

        self._authenticate_user(self.super_user)
        response = self.client.get(
            f"{self.endpoint}/general-visitor/",
            {
                "department_id": self.department.id,
                "facility_id": self.facility.id,
                "user_id": self.super_user.id,
                "category": "Outpatient",
                "incident_type": "General",
            },
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 3)
    
    def test_get_incidents_filter_normalized(self):
        """Test incident retrieval with normalized filters"""
        for _ in range(3):
            GeneralPatientVisitorFactory(
                department=self.department,
                report_facility=self.facility,
                created_by=self.super_user,
                category="Outpatient",
                incident_type="General",
            )

        self._authenticate_user(self.super_user)
        response = self.client.get(
            f"{self.endpoint}/general-visitor/",
            {
                "department_id": self.department.id,
                "facility_id": self.facility.id,
                "user_id": self.super_user.id,
                "category": "oUtpatiEnt",
                "incident_type": "gEnErAl",
            },
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 3)
    
    def test_get_incidents_with_date_range(self):
        """Test incident retrieval with date range filtering"""
        GeneralPatientVisitorFactory(
            incident_date=date.today() - timedelta(days=10),
            department=self.department,
            report_facility=self.facility,
        )
        GeneralPatientVisitorFactory(
            incident_date=date.today() - timedelta(days=5),
            department=self.department,
            report_facility=self.facility,
        )
        GeneralPatientVisitorFactory(
            incident_date=date.today(),
            department=self.department,
            report_facility=self.facility,
        )

        self._authenticate_user(self.super_user)

        start = date.today() - timedelta(days=6)
        response = self.client.get(
            f"{self.endpoint}/general-visitor/",
            {"start_date": start.isoformat()}
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 2)

        end = date.today() - timedelta(days=6)
        response = self.client.get(
            f"{self.endpoint}/general-visitor/",
            {"end_date": end.isoformat()}
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 1)

        start = date.today() - timedelta(days=6)
        end = date.today()
        response = self.client.get(
            f"{self.endpoint}/general-visitor/",
            {
                "start_date": start.isoformat(),
                "end_date": end.isoformat(),
            }
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 2)

        start = date.today() - timedelta(days=100)
        end = date.today() - timedelta(days=50)
        response = self.client.get(
            f"{self.endpoint}/general-visitor/",
            {
                "start_date": start.isoformat(),
                "end_date": end.isoformat(),
            }
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 0)
    
    def test_get_incidents_excludes_null_dates(self):
        """Should exclude incidents with null date field even when in date range"""
        GeneralPatientVisitorFactory(
            department=self.department,
            report_facility=self.facility,
            created_by=self.super_user,
            incident_date=None,
        )
        
        GeneralPatientVisitorFactory(
            department=self.department,
            report_facility=self.facility,
            created_by=self.super_user,
            incident_date=date.today(),
        )

        self._authenticate_user(self.super_user)
        response = self.client.get(
            f"{self.endpoint}/general-visitor/",
            {"start_date": date.today().strftime("%Y-%m-%d")}
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 1)



class GetsGetIncidentDetails(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.endpoint = "/api/incidents"
        self.department = self.super_user_dept
        self.facility = self.super_user_fac

    def test_get_incident_details_as_super_user(self):
        """Test incident retrieval as super user"""
        incident = GeneralPatientVisitorFactory(
            department=self.department, report_facility=self.facility
        )

        self._authenticate_user(self.super_user)
        response = self.client.get(f"{self.endpoint}/general-visitor/{incident.id}/")

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["incident"]["id"], incident.id)


class TestUpdateIncident(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.endpoint = "/api/incidents"
        self.department = self.super_user_dept
        self.facility = self.super_user_fac

    def test_update_incident_as_super_user(self):
        """Test incident update as super user"""
        family_data = {
            "department": self.department.id,
            "family_notified": {
                "first_name": "Jane",
                "last_name": "Smith",
                "email": "<EMAIL>",
                "relationship_to_patient": "Mother",
                "profile_type": "Family",
            },
        }
        incident = GeneralPatientVisitorFactory(
            department=self.department,
            report_facility=self.facility,
            created_by=self.super_user,
        )

        self._authenticate_user(self.super_user)
        response = self.client.put(
            f"{self.endpoint}/general-visitor/{incident.id}/",
            data=family_data,
            format="json",
        )

        self.assertEqual(response.status_code, 200)


# test modifying an incident
class TestModifyIncident(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.endpoint = "/api/incidents"
        self.department = self.super_user_dept
        self.facility = self.super_user_fac

    def test_modify_incident_as_super_user(self):
        """Test modifying an incident as super user"""
        incident = GeneralPatientVisitorFactory(created_by=self.super_user)
        modify_data = {
            "action": "modify",
            "status": "Open",
            "patient_visitor": {
                "first_name": "Ngoga",
                "last_name": "Henry",
                "profile_type": "Patient",
                "medical_record_number": "1234567",
            },
            "physician_notified": {
                "first_name": "Ngoga",
                "last_name": "Henry",
                "profile_type": "Physician",
            },
            "family_notified": {
                "first_name": "Ngoga",
                "last_name": "Henry",
                "profile_type": "Family",
            },
        }

        self._authenticate_user(self.super_user)
        response = self.client.patch(
            f"{self.endpoint}/general-visitor/{incident.id}/",
            data=modify_data,
            format="json",
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["status"], "Open")


# test send for review
class TestSendForReview(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.template = ReviewTemplateFactory(
            name="Test Template", description="Test Desc"
        )
        self.incident = GeneralPatientVisitorFactory()

        # Create profiles and review group
        self.template_profiles = [ProfileFactory() for _ in range(3)]
        self.group = ReviewGroupFactory()
        self.group.members.add(*self.template_profiles)

        # Create a ReviewTemplateTask with number_of_days_to_complete
        self.task = ReviewTemplateTaskFactory(
            review_template=self.template,
            require_approval_for_all_groups=True,
        )
        self.task.review_groups.add(self.group)

    def test_handle_review_template_success(self):
        """Test sending General Patient Visitor incident for review using template"""
        self._authenticate_user(self.admin_user)
        data = {
            "action": "send-for-review",
            "review_template": self.template.id,
        }
        response = self.client.patch(
            f"/api/incidents/general-visitor/{self.incident.id}/",
            data,
            format="json",
        )

        if not response.status_code == 200:
            self.fail(f"Failed to handle review template: {response.data}")

        self.incident.refresh_from_db()
        self.assertEqual(response.status_code, 200)
        self.assertIsNotNone(self.incident.review_process)
        self.assertEqual(self.incident.review_process.name, self.template.name)
        self.assertEqual(
            self.incident.review_process.description, self.template.description
        )

        process_tasks = self.incident.review_process.review_process_tasks.all()
        self.assertEqual(process_tasks.count(), 1)
        process_task = process_tasks.first()

        self.assertEqual(process_task.name, self.task.name)
        self.assertEqual(process_task.description, self.task.description)
        self.assertEqual(process_task.task_priority, self.task.task_priority)
        self.assertEqual(
            process_task.require_approval_for_all_groups,
            self.task.require_approval_for_all_groups,
        )
        self.assertEqual(process_task.status, "Pending")

        expected_deadline = datetime.now().date() + timedelta(
            days=self.task.number_of_days_to_complete
        )
        self.assertEqual(process_task.deadline, expected_deadline)
        self.assertIn(self.group, process_task.review_groups.all())

    def test_send_for_review_with_assignees_success(self):
        """Test sending General Patient Visitor incident for review using assignees"""
        self._authenticate_user(self.admin_user)
        data = {
            "action": "send-for-review",
            "assignees": [profile.id for profile in self.template_profiles],
        }
        response = self.client.patch(
            f"/api/incidents/general-visitor/{self.incident.id}/",
            data,
            format="json",
        )

        if not response.status_code == 200:
            self.fail(f"Failed to send for a review: {response.data}")

        self.assertEqual(response.status_code, 200)


# test marking an incident as closed
class TestMarkAsClosed(BaseTestSetup):
    def setUp(self):
        super().setUp()
        """Set up basic test environment"""

    def test_mark_as_closed_success(self):
        """Test marking a medication error as closed successfully"""
        incident = GeneralPatientVisitorFactory(created_by=self.user_user)
        self._authenticate_user(self.super_user)

        response = self.client.patch(
            f"{self.incidents_endpoint}/general-visitor/{incident.id}/",
            data={
                "action": "mark-closed",
            },
            format="json",
        )

        self.assertEqual(response.status_code, 200)

        # check if the incident is closed successfully
        incident.refresh_from_db()
        self.assertEqual(incident.status, "Closed")


# test deleting draft incidents
