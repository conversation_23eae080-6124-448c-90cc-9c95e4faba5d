from rest_framework import serializers
from accounts.serializers import UserProfileSerializer, UserSerializer
from facilities.serializers import FacilitySerializer
from lost_and_found.models import LostAndFound
from base.serializers import BaseModelSerializer, DepartmentSerializer


class GetLostAndFoundSerializer(BaseModelSerializer):
    reported_by = UserProfileSerializer(read_only=True)
    found_by = UserProfileSerializer(read_only=True)
    taken_by = UserProfileSerializer(read_only=True)
    report_facility = FacilitySerializer(read_only=True)
    department = DepartmentSerializer(read_only=True)

    created_by = UserSerializer(read_only=True)
    updated_by = UserSerializer(read_only=True)

    class Meta:
        model = LostAndFound
        fields = "__all__"

    def get_reported_by(self, obj):
        if obj.reported_by:
            return {
                "id": obj.reported_by.id,
                "first_name": obj.reported_by.first_name,
                "last_name": obj.reported_by.last_name,
                "email": obj.reported_by.email,
            }
        return None

    def get_found_by(self, obj):
        if obj.found_by:
            return {
                "id": obj.found_by.id,
                "first_name": obj.found_by.first_name,
                "last_name": obj.found_by.last_name,
                "email": obj.found_by.email,
            }
        return None
