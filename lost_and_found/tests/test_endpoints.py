from django.urls import reverse
from rest_framework import status
from base.tests.base_setup import BaseTestSetup
from base.tests.factory import UserProfileFactory
from lost_and_found.tests.factory import LostAndFoundFactory
from tasks.tests.factory import ReviewTemplateFactory


class TestLostFoundIncidentsAPI(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.url = "/api/incidents/lost-found/"
        self._authenticate_user(self.super_user)
        taken_by = UserProfileFactory(profile_type="Staff")
        reported_by = UserProfileFactory(profile_type="Staff")
        self.valid_data = {
            "property_name": "Gold Watch",
            "item_description": "18k gold watch with leather strap",
            "report_facility": self.super_user_fac.id,
            "department": self.super_user_dept.id,
            "reported_by": {"user_id": taken_by.id},
            "taken_by": {"user_id": reported_by.id},
            "is_found": True,
            "location_found": "Hospital Cafeteria",
        }

    def test_get_incidents_list(self):
        """Test GET request returns list of incidents"""
        LostAndFoundFactory.create_batch(3, created_by=self.super_user)

        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 3)

    def test_get_incidents_with_filters(self):
        """Test GET request with filters"""
        LostAndFoundFactory(status="Draft", created_by=self.super_user)
        LostAndFoundFactory(status="Open", created_by=self.super_user)

        response = self.client.get(f"{self.url}?status=Draft")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)

    def test_create_incident_success(self):
        """Test POST request creates incident"""
        response = self.client.post(self.url, self.valid_data, format="json")

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(
            response.data["property_name"], self.valid_data["property_name"]
        )

    def test_create_incident_invalid_data(self):
        """Test POST request with invalid data fails"""
        invalid_data = self.valid_data.copy()
        invalid_data.get("taken_by")["user_id"] = None

        response = self.client.post(self.url, invalid_data, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_create_incident_unauthenticated(self):
        """Test POST request without authentication fails"""
        self.client.logout()

        response = self.client.post(self.url, self.valid_data, format="json")

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)


class TestLostFoundIncidentDetailsAPI(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.incident = LostAndFoundFactory(
            created_by=self.super_user,
            department=self.super_user_dept,
            report_facility=self.super_user_fac,
        )
        self.url = f"/api/incidents/lost-found/{self.incident.id}/"
        self._authenticate_user(self.super_user)
        self.update_data = {
            "property_name": "Updated Watch",
            "item_description": "Updated description",
        }

    def test_get_incident_details(self):
        """Test GET request returns incident details"""
        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_get_nonexistent_incident(self):
        """Test GET request for non-existent incident"""
        response = self.client.get("/api/incidents/lost-found/99999/")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_update_incident(self):
        """Test PUT request updates incident"""
        response = self.client.put(self.url, self.update_data, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(
            response.data["property_name"], self.update_data["property_name"]
        )

    def test_patch_send_for_review(self):
        """Test PATCH request sends incident for review"""
        review_template = ReviewTemplateFactory()
        data = {
            "action": "send-for-review",
            "review_template": review_template.id,
            "description": "Hey, please review this",
        }

        response = self.client.patch(self.url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_patch_mark_closed(self):
        """Test PATCH request marks incident as closed"""
        data = {"action": "mark-closed"}

        response = self.client.patch(self.url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_patch_modify_incident(self):
        """Test PATCH request modifies incident"""
        data = {"action": "modify", "property_name": "Modified Watch"}

        response = self.client.patch(self.url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_patch_invalid_action(self):
        """Test PATCH request with invalid action"""
        data = {"action": "invalid-action"}

        response = self.client.patch(self.url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_patch_missing_action(self):
        """Test PATCH request without action"""
        response = self.client.patch(self.url, {}, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
