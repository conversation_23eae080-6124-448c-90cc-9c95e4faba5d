from django.urls import reverse
from rest_framework import status
from base.tests.base_setup import BaseTestSetup
from base.tests.factory import UserProfileFactory
from medication_error.tests.factory import MedicationErrorFactory
from documents.models import Document
from tasks.tests.factory import ReviewTemplateFactory


class TestMedicationErrorIncidentsAPI(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.url = "/api/incidents/medication-error/"
        self._authenticate_user(self.super_user)
        self.patient = UserProfileFactory(profile_type="Patient")
        self.provider = UserProfileFactory(profile_type="Provider")
        self.valid_data = {
            "status": "Draft",
            "report_facility": self.super_user_fac.id,
            "department": self.super_user_dept.id,
            "patient": {"user_id": self.patient.id},
            "provider_info": {"user_id": self.provider.id},
            "drug_ordered": "Test Drug",
            "drug_given": "Wrong Drug",
            "error_category": "Wrong Medication",
        }

    def test_get_incidents_list(self):
        """Test getting list of incidents"""
        MedicationErrorFactory.create_batch(
            3, created_by=self.super_user, report_facility=self.super_user_fac
        )

        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 3)

    def test_get_incidents_with_filters(self):
        """Test getting filtered incidents"""
        MedicationErrorFactory(
            status="Draft",
            created_by=self.super_user,
            report_facility=self.super_user_fac,
        )
        MedicationErrorFactory(
            status="Open",
            created_by=self.super_user,
            report_facility=self.super_user_fac,
        )

        response = self.client.get(f"{self.url}?status=Draft")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)

    def test_create_incident_success(self):
        """Test successful incident creation"""
        response = self.client.post(self.url, self.valid_data, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data["drug_ordered"], self.valid_data["drug_ordered"])

    def test_create_incident_invalid_data(self):
        """Test creation with invalid data"""
        invalid_data = self.valid_data.copy()
        invalid_data.get("patient")["user_id"] = None

        response = self.client.post(self.url, invalid_data, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_create_incident_unauthorized(self):
        """Test creation without authentication"""
        self.client.logout()

        response = self.client.post(self.url, self.valid_data, format="json")

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)


class TestMedicationErrorIncidentDetailsAPI(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.incident = MedicationErrorFactory(
            created_by=self.super_user,
            report_facility=self.super_user_fac,
            department=self.super_user_dept,
        )
        self.url = f"/api/incidents/medication-error/{self.incident.id}/"
        self._authenticate_user(self.super_user)
        self.update_data = {
            "drug_ordered": "Updated Drug",
            "error_category": "Updated Category",
        }

    def test_get_incident_details(self):
        """Test getting single incident details"""
        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_get_nonexistent_incident(self):
        """Test getting non-existent incident"""
        response = self.client.get("/api/incidents/medication-error/99999/")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_update_incident(self):
        """Test updating incident"""
        response = self.client.put(self.url, self.update_data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(
            response.data["drug_ordered"], self.update_data["drug_ordered"]
        )

    def test_send_for_review(self):
        """Test sending incident for review"""
        review_template = ReviewTemplateFactory()
        data = {
            "action": "send-for-review",
            "review_template": review_template.id,
            "description": "Test description for review",
        }

        response = self.client.patch(self.url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_mark_closed(self):
        """Test marking incident as closed"""
        data = {"action": "mark-closed", "report_facility": self.super_user_fac.id}

        response = self.client.patch(self.url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)

    # def test_delete_draft(self):
    #     """Test deleting draft incident"""
    #     data = {
    #         "action": "delete-draft",
    #         "report_facility": self.super_user_fac.id
    #     }

    #     response = self.client.patch(self.url, data, format='json')
    #     self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_invalid_action(self):
        """Test PATCH with invalid action"""
        data = {"action": "invalid-action", "report_facility": self.super_user_fac.id}

        response = self.client.patch(self.url, data, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data["error"], "Invalid action.")

    def test_missing_action(self):
        """Test PATCH without action"""
        response = self.client.patch(self.url, {}, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data["error"], "Action is required.")
