import os
import uuid
from base.services.responses import APIResponse
from base.services.logging.logger import LoggingService
from documents.models import Document
from documents.services.operations import DocumentsOperations
from documents.views import upload_file
from patient_visitor_grievance.models import GrievanceInvestigation
from api.views.auth.permissions_list import (
    is_admin_user,
    is_manager_user,
    is_super_user,
)
from accounts.models import Profile


class GrievanceInvestigationDocumentService:
    """Service class for handling grievance investigation documents including extension and response letters"""
    
    def __init__(self, user, investigation_id=None):
        self.user = user
        self.investigation_id = investigation_id
        self.logging_service = LoggingService()
        self.doc_operations = DocumentsOperations()

    def _check_permissions(self, investigation):
        """Check if user has permission to modify investigation documents"""
        try:
            profile = Profile.objects.get(user=self.user)
            return (
                is_super_user(self.user)
                or is_admin_user(self.user, investigation.report_facility)
                or is_manager_user(self.user, investigation.department)
                or self.user == investigation.created_by
            )
        except Profile.DoesNotExist:
            return False

    def upload_extension_letter(self, file) -> APIResponse:
        """Upload extension letter copy for grievance investigation"""
        try:
            investigation = GrievanceInvestigation.objects.get(id=self.investigation_id)
            
            if not self._check_permissions(investigation):
                return APIResponse(
                    success=False,
                    message="You do not have permission to upload documents to this investigation",
                    data=None,
                    code=403,
                )

            unique_filename = f"{uuid.uuid4().hex}_{file.name}"
            file.seek(0)
            base_name, extension = os.path.splitext(unique_filename)

            if len(base_name) > 200:
                base_name = base_name[:200]

            is_uploaded, document_url, public_id, message = upload_file(
                file, "grievance-investigation"
            )

            if not is_uploaded:
                self.logging_service.log_error(message)
                return APIResponse(
                    success=False,
                    message="Failed to upload file",
                    data=None,
                    code=500,
                )

            document = Document.objects.create(
                name=base_name,
                file_type=extension,
                created_by=self.user,
                document_url=document_url,
                public_id=public_id,
            )

            investigation.extension_letter_copy = document
            investigation.save()

            return APIResponse(
                success=True,
                message="Extension letter uploaded successfully",
                data={
                    "id": document.id,
                    "name": document.name,
                    "file_type": document.file_type,
                    "document_url": document.document_url,
                    "created_at": document.created_at,
                    "updated_at": document.updated_at,
                },
                code=201,
            )

        except GrievanceInvestigation.DoesNotExist:
            return APIResponse(
                success=False,
                message="Grievance investigation not found",
                data=None,
                code=404,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Internal server error",
                data=None,
                code=500,
            )

    def upload_response_letter(self, file) -> APIResponse:
        """Upload response letter copy for grievance investigation"""
        try:
            investigation = GrievanceInvestigation.objects.get(id=self.investigation_id)
            
            if not self._check_permissions(investigation):
                return APIResponse(
                    success=False,
                    message="You do not have permission to upload documents to this investigation",
                    data=None,
                    code=403,
                )

            unique_filename = f"{uuid.uuid4().hex}_{file.name}"
            file.seek(0)
            base_name, extension = os.path.splitext(unique_filename)

            if len(base_name) > 200:
                base_name = base_name[:200]

            is_uploaded, document_url, public_id, message = upload_file(
                file, "grievance-investigation"
            )

            if not is_uploaded:
                self.logging_service.log_error(message)
                return APIResponse(
                    success=False,
                    message="Failed to upload file",
                    data=None,
                    code=500,
                )

            document = Document.objects.create(
                name=base_name,
                file_type=extension,
                created_by=self.user,
                document_url=document_url,
                public_id=public_id,
            )

            investigation.response_letter_copy = document
            investigation.save()

            return APIResponse(
                success=True,
                message="Response letter uploaded successfully",
                data={
                    "id": document.id,
                    "name": document.name,
                    "file_type": document.file_type,
                    "document_url": document.document_url,
                    "created_at": document.created_at,
                    "updated_at": document.updated_at,
                },
                code=201,
            )

        except GrievanceInvestigation.DoesNotExist:
            return APIResponse(
                success=False,
                message="Grievance investigation not found",
                data=None,
                code=404,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Internal server error",
                data=None,
                code=500,
            )

    def get_extension_letter(self) -> APIResponse:
        """Get extension letter copy for grievance investigation"""
        try:
            investigation = GrievanceInvestigation.objects.get(id=self.investigation_id)
            
            if not investigation.extension_letter_copy:
                return APIResponse(
                    success=False,
                    message="No extension letter found for this investigation",
                    data=None,
                    code=404,
                )

            document = investigation.extension_letter_copy
            return APIResponse(
                success=True,
                message="Extension letter retrieved successfully",
                data={
                    "id": document.id,
                    "name": document.name,
                    "file_type": document.file_type,
                    "document_url": document.document_url,
                    "created_at": document.created_at,
                    "updated_at": document.updated_at,
                    "created_by": {
                        "id": document.created_by.id,
                        "first_name": document.created_by.first_name,
                        "last_name": document.created_by.last_name,
                    } if document.created_by else None,
                },
                code=200,
            )

        except GrievanceInvestigation.DoesNotExist:
            return APIResponse(
                success=False,
                message="Grievance investigation not found",
                data=None,
                code=404,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Internal server error",
                data=None,
                code=500,
            )

    def get_response_letter(self) -> APIResponse:
        """Get response letter copy for grievance investigation"""
        try:
            investigation = GrievanceInvestigation.objects.get(id=self.investigation_id)
            
            if not investigation.response_letter_copy:
                return APIResponse(
                    success=False,
                    message="No response letter found for this investigation",
                    data=None,
                    code=404,
                )

            document = investigation.response_letter_copy
            return APIResponse(
                success=True,
                message="Response letter retrieved successfully",
                data={
                    "id": document.id,
                    "name": document.name,
                    "file_type": document.file_type,
                    "document_url": document.document_url,
                    "created_at": document.created_at,
                    "updated_at": document.updated_at,
                    "created_by": {
                        "id": document.created_by.id,
                        "first_name": document.created_by.first_name,
                        "last_name": document.created_by.last_name,
                    } if document.created_by else None,
                },
                code=200,
            )

        except GrievanceInvestigation.DoesNotExist:
            return APIResponse(
                success=False,
                message="Grievance investigation not found",
                data=None,
                code=404,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Internal server error",
                data=None,
                code=500,
            )

    def delete_extension_letter(self) -> APIResponse:
        """Delete extension letter copy for grievance investigation"""
        try:
            investigation = GrievanceInvestigation.objects.get(id=self.investigation_id)

            if not self._check_permissions(investigation):
                return APIResponse(
                    success=False,
                    message="You do not have permission to delete documents from this investigation",
                    data=None,
                    code=403,
                )

            if investigation.extension_letter_copy:
                document = investigation.extension_letter_copy
                investigation.extension_letter_copy = None
                investigation.save()
                document.delete()

                return APIResponse(
                    success=True,
                    message="Extension letter deleted successfully",
                    data=None,
                    code=200,
                )
            else:
                return APIResponse(
                    success=False,
                    message="No extension letter found to delete",
                    data=None,
                    code=404,
                )

        except GrievanceInvestigation.DoesNotExist:
            return APIResponse(
                success=False,
                message="Grievance investigation not found",
                data=None,
                code=404,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Internal server error",
                data=None,
                code=500,
            )

    def delete_response_letter(self) -> APIResponse:
        """Delete response letter copy for grievance investigation"""
        try:
            investigation = GrievanceInvestigation.objects.get(id=self.investigation_id)

            if not self._check_permissions(investigation):
                return APIResponse(
                    success=False,
                    message="You do not have permission to delete documents from this investigation",
                    data=None,
                    code=403,
                )

            if investigation.response_letter_copy:
                document = investigation.response_letter_copy
                investigation.response_letter_copy = None
                investigation.save()
                document.delete()

                return APIResponse(
                    success=True,
                    message="Response letter deleted successfully",
                    data=None,
                    code=200,
                )
            else:
                return APIResponse(
                    success=False,
                    message="No response letter found to delete",
                    data=None,
                    code=404,
                )

        except GrievanceInvestigation.DoesNotExist:
            return APIResponse(
                success=False,
                message="Grievance investigation not found",
                data=None,
                code=404,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Internal server error",
                data=None,
                code=500,
            )

    def upload_multiple_documents(self, files) -> APIResponse:
        """Upload multiple documents for grievance investigation"""
        pass